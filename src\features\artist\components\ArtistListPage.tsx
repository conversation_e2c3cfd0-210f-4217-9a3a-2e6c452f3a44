"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Search, Users, Filter } from "lucide-react"
import { artistsData } from "../utils/constants"
import { ArtistCard } from "./ArtistCard"


export default function ArtistsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("")
  const [locationFilter, setLocationFilter] = useState<string>("")

  // Get unique locations for filter
  const uniqueLocations = Array.from(new Set(artistsData.map((artist) => artist.location))).sort()

  // Filter artists based on search and filters
  const filteredArtists = artistsData.filter((artist) => {
    const matchesSearch =
      artist.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      artist.bio.toLowerCase().includes(searchTerm.toLowerCase()) ||
      artist.location.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus =
      !statusFilter ||
      (statusFilter === "active" && !artist.disbandedDate) ||
      (statusFilter === "disbanded" && artist.disbandedDate)

    const matchesLocation = !locationFilter || artist.location === locationFilter

    return matchesSearch && matchesStatus && matchesLocation
  })

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Users className="w-8 h-8 text-primary" />
            <h1 className="text-4xl font-bold">Artists</h1>
          </div>
          <p className="text-muted-foreground text-lg">
            Discover amazing artists from around the world. Browse through our collection of talented musicians and
            bands.
          </p>
        </div>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="pt-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search artists by name, bio, or location..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="disbanded">Disbanded</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={locationFilter} onValueChange={setLocationFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Locations</SelectItem>
                    {uniqueLocations.map((location) => (
                      <SelectItem key={location} value={location}>
                        {location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Filter Summary */}
            <div className="flex items-center gap-4 mt-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Filter className="w-4 h-4" />
                <span>
                  Showing {filteredArtists.length} of {artistsData.length} artists
                </span>
              </div>
              {searchTerm && <span>• Searching for {searchTerm}</span>}
              {statusFilter && statusFilter !== "all" && <span>• Status: {statusFilter}</span>}
              {locationFilter && locationFilter !== "all" && <span>• Location: {locationFilter}</span>}
            </div>
          </CardContent>
        </Card>

        {/* Artists Grid */}
        {filteredArtists.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredArtists.map((artist) => (
              <ArtistCard key={artist.id} artist={artist} />
            ))}
          </div>
        ) : (
          <Card className="text-center py-12">
            <CardContent>
              <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No artists found</h3>
              <p className="text-muted-foreground">
                {searchTerm || statusFilter || locationFilter
                  ? "Try adjusting your search criteria or filters."
                  : "No artists available at the moment."}
              </p>
            </CardContent>
          </Card>
        )}

        {/* Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">{artistsData.length}</div>
              <div className="text-muted-foreground">Total Artists</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">
                {artistsData.filter((artist) => !artist.disbandedDate).length}
              </div>
              <div className="text-muted-foreground">Active Artists</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6 text-center">
              <div className="text-3xl font-bold text-primary mb-2">{uniqueLocations.length}</div>
              <div className="text-muted-foreground">Countries/Regions</div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
