'use client';

import { useTranslations } from 'next-intl';
import { Card } from "@/components/ui/card";
import { Album, Briefcase, PlayCircle, Send, User } from "lucide-react";
import { useRouter } from "next/navigation";
import { client } from '@/graphql-client';
import { getAllArtist } from '@/graphql/queries';
import { useEffect } from 'react';

export default function HomePage() {
  const t = useTranslations('HomePage');
  const router = useRouter();

  const handleArtistClick = () => {
    router.push(`/artist`);
  };
  const handleAlbumClick = () => {
    router.push(`/album`);
  };
  const handleOpportunityClick = () => {
    router.push(`/opportunities`);
  };
  const handleApplicationClick = () => {
    router.push(`/applications`);
  };
  const handlePlaylistClick = () => {
    router.push(`/playlist`);
  };

  
  const fetchArtists = async () => {
    try {
      const result = await client.graphql({
        query: getAllArtist,
        variables: {
          input: {
            name: "My first todo!"
          }
        }
      });
      console.log("🚀 ~ fetchArtists result:", result);
    } catch (error) {
      console.error("Error fetching artists:", error);
    }
  };

  // Call function on initial render
  useEffect(() => {
    fetchArtists();
  }, []);
  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Main Content */}
      <main className="container mx-auto px-6 py-12">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            {t('title')}
          </h1>
          
          <p className="text-xl text-muted-foreground mb-12 max-w-2xl mx-auto">
            {t('subtitle')}
          </p>

          {/* Feature Cards */}
<div className="grid md:grid-cols-3 gap-6 mb-12">
  <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handlePlaylistClick()}>
    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
      <PlayCircle />
    </div>
    <h3 className="text-lg font-semibold mb-2">Playlist</h3>
    <p className="text-muted-foreground">Create and enjoy personalized playlists curated for your taste.</p>
  </Card>

  <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleArtistClick()}>
    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
      <User />
    </div>
    <h3 className="text-lg font-semibold mb-2">Artist</h3>
    <p className="text-muted-foreground">Discover top artists, explore their profiles, and follow your favorites.</p>
  </Card>

  <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleAlbumClick()}>
    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
      <Album />
    </div>
    <h3 className="text-lg font-semibold mb-2">Album</h3>
    <p className="text-muted-foreground">Browse and listen to curated albums across various genres.</p>
  </Card>

  <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleOpportunityClick()}>
    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
      <Briefcase />
    </div>
    <h3 className="text-lg font-semibold mb-2">Opportunities</h3>
    <p className="text-muted-foreground">Explore the latest career openings and project opportunities.</p>
  </Card>

  <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleApplicationClick()}>
    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
      <Send />
    </div>
    <h3 className="text-lg font-semibold mb-2">My Applications</h3>
    <p className="text-muted-foreground">Track the status and progress of your submitted job applications.</p>
  </Card>
</div>


       
        </div>
      </main>

    </div>
  );
}
