"use client";
import {
  Calendar,
  Home,
  Search,
  Settings,
  User,
  Guitar,
  MessageCircle,
  Users,
  DollarSign,
  HelpCircle,
  Menu
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { isAuthOnlyRoute } from "@/lib/auth-routes";
import { usePathname } from "next/navigation";

// Main navigation items
const mainMenuItems = [
  {
    title: "Dashboard",
    url: "/",
    icon: Home,
  },
  {
    title: "Discover",
    url: "#",
    icon: Search,
  },
  {
    title: "Gigs",
    url: "#",
    icon: Calendar,
  },
  {
    title: "Studio",
    url: "#",
    icon: Guitar,
  },
  {
    title: "Chat",
    url: "#",
    icon: MessageCircle,
  },
  {
    title: "Collaborator",
    url: "#",
    icon: User,
  },
  {
    title: "Community",
    url: "#",
    icon: Users,
  },
  {
    title: "Pricing",
    url: "#",
    icon: DollarSign,
  },
]

// Footer items
const footerMenuItems = [
  {
    title: "Support",
    url: "#",
    icon: HelpCircle,
  },
  {
    title: "Settings",
    url: "#",
    icon: Settings,
  },
]

export function AppSidebar() {
  const pathname = usePathname()

  if(isAuthOnlyRoute(pathname)){
    return null;
  }

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <div className="flex items-center justify-center p-2">
          <Menu className="h-6 w-6" />
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {mainMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    tooltip={item.title}
                  >
                    <a href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {footerMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    tooltip={item.title}
                  >
                    <a href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarFooter>
    </Sidebar>
  )
}