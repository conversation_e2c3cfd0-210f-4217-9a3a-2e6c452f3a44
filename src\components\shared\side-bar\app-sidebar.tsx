"use client";
import {
  Calendar,
  Home,
  Search,
  Settings,
  User,
  Guitar,
  MessageCircle,
  Users,
  DollarSign,
  HelpCircle,
  Menu
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { isAuthOnlyRoute } from "@/lib/auth-routes";
import { usePathname } from "next/navigation";

// Main navigation items
const mainMenuItems = [
  {
    title: "Dashboard",
    url: "/",
    icon: Home,
  },
  {
    title: "Discover",
    url: "#",
    icon: Search,
  },
  {
    title: "Gigs",
    url: "#",
    icon: Calendar,
  },
  {
    title: "Studio",
    url: "#",
    icon: Guitar,
  },
  {
    title: "Chat",
    url: "#",
    icon: MessageCircle,
  },
  {
    title: "Collaborator",
    url: "#",
    icon: User,
  },
  {
    title: "Community",
    url: "#",
    icon: Users,
  },
  {
    title: "Pricing",
    url: "#",
    icon: DollarSign,
  },
]

// Footer items
const footerMenuItems = [
  {
    title: "Support",
    url: "#",
    icon: HelpCircle,
  },
  {
    title: "Settings",
    url: "#",
    icon: Settings,
  },
]

export function AppSidebar() {
  const pathname = usePathname()

  if(isAuthOnlyRoute(pathname)){
    return null;
  }

  return (
    <Sidebar collapsible="none" className="border-r w-20">
      <SidebarHeader className="border-b p-2">
        <div className="flex items-center justify-center h-12">
          <Menu className="h-6 w-6" />
        </div>
      </SidebarHeader>

      <SidebarContent className="px-2 py-4">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-2">
              {mainMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className="flex flex-col items-center justify-center h-14 w-full hover:bg-muted rounded-lg"
                    tooltip={item.title}
                  >
                    <a href={item.url} className="flex flex-col items-center gap-1">
                      <item.icon className="h-5 w-5" />
                      <span className="text-xs text-muted-foreground">
                        {item.title}
                      </span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t p-2">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-2">
              {footerMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className="flex flex-col items-center justify-center h-14 w-full hover:bg-muted rounded-lg"
                    tooltip={item.title}
                  >
                    <a href={item.url} className="flex flex-col items-center gap-1">
                      <item.icon className="h-5 w-5" />
                      <span className="text-xs text-muted-foreground">
                        {item.title}
                      </span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarFooter>
    </Sidebar>
  )
}