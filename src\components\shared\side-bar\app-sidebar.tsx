"use client";
import {
  Calendar,
  Home,
  Search,
  Settings,
  User,
  Guitar,
  MessageCircle,
  Users,
  DollarSign,
  HelpCircle,
  Menu
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { isAuthOnlyRoute } from "@/lib/auth-routes";
import { usePathname } from "next/navigation";

// Main navigation items
const mainMenuItems = [
  {
    title: "Dashboard",
    url: "/",
    icon: Home,
  },
  {
    title: "Discover",
    url: "#",
    icon: Search,
  },
  {
    title: "Gigs",
    url: "#",
    icon: Calendar,
  },
  {
    title: "Studio",
    url: "#",
    icon: Guitar,
  },
  {
    title: "Chat",
    url: "#",
    icon: MessageCircle,
  },
  {
    title: "Collaborator",
    url: "#",
    icon: User,
  },
  {
    title: "Community",
    url: "#",
    icon: Users,
  },
  {
    title: "Pricing",
    url: "#",
    icon: DollarSign,
  },
]

// Footer items
const footerMenuItems = [
  {
    title: "Support",
    url: "#",
    icon: HelpCircle,
  },
  {
    title: "Settings",
    url: "#",
    icon: Settings,
  },
]

export function AppSidebar() {
  const pathname = usePathname()

  if(isAuthOnlyRoute(pathname)){
    return null;
  }

  return (
    <Sidebar collapsible="none" className="w-20">
      <SidebarHeader className="border-b">
        <div className="flex items-center justify-center py-4">
          <Menu className="h-5 w-5" />
        </div>
      </SidebarHeader>

      <SidebarContent className="px-2 py-4">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {mainMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className="h-16 flex-col gap-1 text-xs"
                  >
                    <a href={item.url} className="flex flex-col items-center gap-1">
                      <item.icon className="h-5 w-5" />
                      <span className="text-xs text-center leading-tight">{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t px-2 py-4">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {footerMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    className="h-16 flex-col gap-1 text-xs"
                  >
                    <a href={item.url} className="flex flex-col items-center gap-1">
                      <item.icon className="h-5 w-5" />
                      <span className="text-xs text-center leading-tight">{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarFooter>
    </Sidebar>
  )
}