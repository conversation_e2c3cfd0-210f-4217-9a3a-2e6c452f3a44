import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/theme/ThemeContext";
import { AuthProvider } from "@/contexts/auth/AuthContext";
import { I18nProvider } from "@/contexts/i18n/I18nContext";
import { MusicPlayerProvider } from "@/contexts/music-player-context/music-player-context"
import Header from "@/components/shared/Header";
import { AuthWrapper } from "@/components/auth/AuthWrapper";
import { defaultLocale } from "@/i18n/config";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/shared/side-bar/app-sidebar";
import { MainContent } from "@/components/shared/MainContent";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Smash Music",
  description: "A modern music application with dark/light theme support",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // For static export, we use the default locale
  // Client-side locale switching will be handled by the LanguageToggle component
  const locale = defaultLocale;

  return (
    <html lang={locale}>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SidebarProvider>
          <AppSidebar />
          <MainContent>
            <I18nProvider>
              <ThemeProvider>
                <AuthProvider>
                  <MusicPlayerProvider>
                    <Header />
                    <AuthWrapper>
                      {children}
                    </AuthWrapper>
                  </MusicPlayerProvider>
                </AuthProvider>
              </ThemeProvider>
            </I18nProvider>
          </MainContent>
        </SidebarProvider>
      </body>
    </html>
  );
}
