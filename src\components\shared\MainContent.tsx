'use client';

import { usePathname } from "next/navigation";
import { isAuthOnlyRoute } from "@/lib/auth-routes";

interface MainContentProps {
  children: React.ReactNode;
}

export function MainContent({ children }: MainContentProps) {
  const pathname = usePathname();
  const isAuthRoute = isAuthOnlyRoute(pathname);

  return (
    <div className={isAuthRoute ? "" : "ml-20"}>
      {children}
    </div>
  );
}
